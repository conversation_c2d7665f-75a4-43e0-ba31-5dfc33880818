"""
非对称噪声配置模块

该模块实现了带有加性偏差的非对称噪声模型，主要用于模拟传感器噪声和系统偏差。
在水下机器人仿真中，这种噪声模型可以更真实地模拟传感器的不确定性和漂移。

作者: Isaac Lab团队
功能:
- 实现带偏差的噪声模型
- 支持非对称高斯噪声
- 提供环境重置时的偏差重新采样
"""

import torch

from dataclasses import MISSING
from collections.abc import Sequence

from isaaclab.utils import configclass
from isaaclab.utils.noise import GaussianNoiseCfg, NoiseModelWithAdditiveBiasCfg, NoiseModel, NoiseModelCfg, NoiseModelWithAdditiveBias, NoiseCfg, gaussian_noise

class AsymmetricNoiseModelWithAdditiveBias(NoiseModel):
    """带有加性偏差的非对称噪声模型

    该类实现了一个噪声模型，在基础噪声的基础上添加了偏差项。
    偏差项在环境重置时从指定的分布中重新采样，用于模拟传感器的系统性偏差。

    主要特点:
    - 继承自基础噪声模型
    - 添加可配置的偏差项
    - 支持多环境并行处理
    """

    def __init__(self, num_envs: int, noise_model_cfg: NoiseModelCfg, device: str):
        """初始化非对称噪声模型

        Args:
            num_envs: 环境数量
            noise_model_cfg: 噪声模型配置
            device: 计算设备(CPU或GPU)
        """
        super().__init__(num_envs, noise_model_cfg)
        self._device = device
        # 偏差噪声配置，定义偏差项的分布特性
        self._bias_noise_cfg = noise_model_cfg.bias_noise_cfg
        # 初始化偏差张量，每个环境都有独立的偏差值
        self._bias = torch.zeros((num_envs, noise_model_cfg.dims), device=self._device)

    def apply(self, data: torch.Tensor) -> torch.Tensor:
        """应用噪声和偏差到输入数据

        该方法首先应用基础噪声模型，然后添加偏差项。

        Args:
            data: 需要添加噪声的数据，形状为 (num_envs, *data_shape)

        Returns:
            添加了噪声和偏差的数据张量
        """
        return super().apply(data) + self._bias

    def reset(self, env_ids: Sequence[int]):
        """重置指定环境的噪声模型

        该方法重新采样指定环境的偏差项，通常在环境重置时调用。
        这样可以模拟传感器偏差在不同任务执行过程中的变化。

        Args:
            env_ids: 需要重置噪声模型的环境ID列表
        """
        self._bias[env_ids] = self._bias_noise_cfg.func(self._bias[env_ids], self._bias_noise_cfg)

@configclass
class AsymmetricNoiseModelWithAdditiveBiasCfg(NoiseModelCfg):
    """带有加性偏差的非对称噪声模型配置类

    该配置类定义了非对称噪声模型的参数，包括偏差噪声配置和数据维度。
    使用@configclass装饰器使其成为可配置的数据类。
    """

    # 指定对应的噪声模型类
    class_type: type = AsymmetricNoiseModelWithAdditiveBias

    # 偏差噪声配置，必须在使用时指定
    bias_noise_cfg: NoiseCfg = MISSING

    # 数据维度，默认为1维
    dims: int = 1

def asymmetric_gaussian_noise(data: torch.Tensor, cfg: NoiseCfg) -> torch.Tensor:
    """非对称高斯噪声函数

    该函数实现了非对称高斯噪声的生成，支持多种操作模式。
    与标准高斯噪声不同，该函数允许均值和标准差在不同环境间变化。

    Args:
        data: 输入数据张量
        cfg: 噪声配置，包含均值、标准差和操作类型

    Returns:
        添加了非对称高斯噪声的数据张量

    Raises:
        ValueError: 当操作类型未知时抛出异常
    """
    # 将均值和标准差扩展到与数据相同的批次大小
    mean = cfg.mean.repeat(data.shape[0],1)
    std = cfg.std.repeat(data.shape[0],1)

    if cfg.operation == "add":
        # 加性噪声: 原数据 + 均值 + 标准差*随机噪声
        return data + mean + std * torch.randn_like(data)
    elif cfg.operation == "scale":
        # 乘性噪声: 原数据 * (均值 + 标准差*随机噪声)
        return data * (mean + std * torch.randn_like(data))
    elif cfg.operation == "abs":
        # 绝对噪声: 仅返回噪声项，忽略原数据
        return mean + std * torch.randn_like(data)
    else:
        raise ValueError(f"Unknown operation in noise: {cfg.operation}")

@configclass
class AsymmetricGaussianNoiseCfg(NoiseCfg):
    """非对称高斯噪声配置类

    该配置类定义了非对称高斯噪声的参数，包括均值和标准差。
    支持张量或标量形式的参数，以实现不同环境间的参数变化。
    """

    # 指定使用的噪声函数
    func = asymmetric_gaussian_noise

    # 噪声均值，可以是张量或标量，必须在使用时指定
    mean: torch.Tensor | float = MISSING
    """噪声的均值参数。默认值为0.0"""

    # 噪声标准差，可以是张量或标量，必须在使用时指定
    std: torch.Tensor | float = MISSING
    """噪声的标准差参数。默认值为1.0"""