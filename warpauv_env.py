"""
WarpAUV水下机器人仿真环境 - IsaacLab版本

该模块实现了基于IsaacLab的WarpAUV水下机器人强化学习环境。
主要功能包括：
- 完整的水下机器人物理仿真
- 流体力学效应建模（浮力、阻力、粘性力）
- 推进器动力学建模
- 强化学习环境接口
- 可视化和调试工具
- 领域随机化支持

作者: <PERSON> and <PERSON> "Veevee" <PERSON><PERSON> (<EMAIL>)
"""

from __future__ import annotations

import random
import math
import torch
from collections.abc import Sequence

# WarpAUV资产配置
from .assets.warpauv import WARPAUV_CFG

# IsaacLab核心模块
import isaaclab.sim as sim_utils
from isaaclab.assets import RigidObject, RigidObjectCfg
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.envs.ui import BaseEnvWindow
from isaaclab.sim import SimulationCfg
from isaaclab.sim.spawners.from_files import GroundPlaneCfg, spawn_ground_plane
from isaaclab.utils import configclass
from isaaclab.utils.math import sample_uniform, normalize

# 可视化和标记
from isaaclab.markers import CUBOID_MARKER_CFG, VisualizationMarkers, RED_ARROW_X_MARKER_CFG, GREEN_ARROW_X_MARKER_CFG, BLUE_ARROW_X_MARKER_CFG

# 数学工具
from isaaclab.utils.math import quat_apply, quat_conjugate, quat_from_angle_axis, quat_mul
import isaaclab.utils.math as math_utils

# 标准库
import gymnasium as gym
import numpy as np

##
# 流体力学模型
##
from isaaclab.utils.math import quat_apply, quat_conjugate
from .rigid_body_hydrodynamics import HydrodynamicForceModels
from .thruster_dynamics import DynamicsFirstOrder, ConversionFunctionBasic, get_thruster_com_and_orientations

class WarpAUVEnvWindow(BaseEnvWindow):
    """WarpAUV环境的窗口管理器

    该类负责管理WarpAUV环境的用户界面窗口，包括调试可视化元素。
    继承自IsaacLab的基础环境窗口类。
    """

    def __init__(self, env: WarpAUVEnv, window_name: str = "IsaacLab"):
        """初始化窗口管理器

        Args:
            env: WarpAUV环境对象
            window_name: 窗口名称，默认为"IsaacLab"
        """
        # 初始化基础窗口
        super().__init__(env, window_name)
        # 添加自定义UI元素
        with self.ui_window_elements["main_vstack"]:
            with self.ui_window_elements["debug_frame"]:
                with self.ui_window_elements["debug_vstack"]:
                    # 添加命令管理器可视化
                    self._create_debug_vis_ui_element("targets", self.env)

@configclass
class WarpAUVEnvCfg(DirectRLEnvCfg):
    """WarpAUV环境配置类

    该配置类定义了WarpAUV环境的所有参数，包括：
    - 仿真参数
    - 机器人配置
    - 场景设置
    - 观测和动作空间
    - 奖励函数参数
    - 物理参数
    - 领域随机化设置
    """

    # UI窗口类型
    ui_window_class_type = WarpAUVEnvWindow

    # 仿真配置：120Hz更新频率
    sim: SimulationCfg = SimulationCfg(dt=1 / 120)

    # 机器人配置
    robot_cfg: RigidObjectCfg = WARPAUV_CFG.replace(prim_path="/World/envs/env_.*/Robot")

    # 场景配置：4个环境，间距4米，复制物理属性
    scene: InteractiveSceneCfg = InteractiveSceneCfg(num_envs=4, env_spacing=4.0, replicate_physics=True)
    debug_vis = True  # 启用调试可视化

    # 强化学习空间定义
    observation_space: gym.spaces.Space = gym.spaces.Box(low=-np.inf, high=np.inf, shape=(17,), dtype=np.float64)  # 观测空间：17维
    action_space: gym.spaces.Space = gym.spaces.Box(low=-1.0, high=1.0, shape=(6,), dtype=np.float64)              # 动作空间：6个推进器，范围[-1,1]
    state_space: gym.spaces.Space = gym.spaces.Box(low=-np.inf, high=np.inf, shape=(17,), dtype=np.float64)        # 状态空间：17维
    # 环境参数
    decimation = 2                      # 动作重复次数
    cap_episode_length = True           # 是否限制回合长度
    episode_length_s = 3.0             # 回合长度（秒）
    episode_length_before_reset = None  # 强制重置前的回合长度
    num_actions = 6                     # 动作数量（6个推进器）
    num_observations = 17               # 观测数量
    num_states = 0                      # 状态数量
    use_boundaries = True               # 是否使用边界限制
    max_auv_x = 7                      # X方向最大距离
    max_auv_y = 7                      # Y方向最大距离
    max_auv_z = 7                      # Z方向最大距离
    starting_depth = 8                  # 起始深度
    min_goal_steps = 100               # 最小目标步数
    goal_completion_radius = 0.01       # 目标完成半径
    goal_dims = 4                       # 目标维度（四元数）
    eval_mode = False                   # 是否为评估模式

    # 初始化参数
    goal_spawn_radius = 2.0             # 目标生成半径
    init_guidance_rate = 0.1            # 初始引导率
    init_vel_max = 1.0                  # 初始最大速度

    # 奖励函数权重
    rew_scale_terminated = 0.0          # 终止奖励权重
    rew_scale_alive = 0.0              # 存活奖励权重
    rew_scale_completion = 1000         # 完成奖励权重

    rew_scale_pos = 0.2                # 位置奖励权重
    rew_scale_ang = 0.5                # 角度奖励权重
    rew_scale_vel = 0.0                # 速度奖励权重
    rew_scale_ang_vel = 0.0            # 角速度奖励权重
    rew_scale_lin_vel = 0.0            # 线速度奖励权重
    rew_scale_actions = 0.2            # 动作奖励权重（能耗惩罚）

    # 动力学参数
    com_to_cob_offset = [0.0, 0.0, 0.01]    # 质心到浮心的偏移量（米），添加到COM得到COB位置
    water_rho = 997.0                        # 水密度 kg/m³
    water_beta = 0.001306                    # 水的动力粘度 Pa·s，50°F时的值
    rotor_constant = 0.1 / 100.0            # Gazebo中使用的转子常数，注意除以100因为0.04比实际值大10倍
    dyn_time_constant = 0.05                # 每个转子线性动力学的时间常数
    volume = 0.022747843530591776           # 体积（立方米）- 中性浮力设计。原始仿真文件中体积=0.0223
    # volume = 0.03
    mass = 2.2701e+01                       # 质量 kg

    # 领域随机化配置
    # TODO: IsaacLab有内置的方法来处理这个
    class domain_randomization:
        """领域随机化参数类

        定义了用于增强训练鲁棒性的随机化参数。
        通过在训练过程中随机化物理参数，提高策略的泛化能力。
        """
        use_custom_randomization = True

        # 保守的随机化设置（注释掉的部分）
        # com_to_cob_offset_radius = 0                                    # 质心到浮心偏移的随机化半径
        # volume_range = [0.022747843530591776, 0.022747843530591776]     # 体积随机化范围 [下界, 上界]
        # mass_range = [2.2701e+0,2.2701e+0]                            # 质量随机化范围 [下界, 上界]

        # 当前使用的随机化设置
        com_to_cob_offset_radius = 0.05                                  # 质心到浮心偏移的随机化半径
        volume_range = [0.019747843530591773, 0.02574784353059178]      # 体积随机化范围 [下界, 上界]
        mass_range = [2.2701e+01,2.2701e+01]                           # 质量随机化范围 [下界, 上界]


class WarpAUVEnv(DirectRLEnv):
    """WarpAUV水下机器人强化学习环境

    该类实现了完整的WarpAUV水下机器人仿真环境，包括：
    - 物理仿真和动力学建模
    - 流体力学效应计算
    - 推进器控制系统
    - 强化学习接口
    - 可视化和调试功能
    - 领域随机化支持

    继承自IsaacLab的DirectRLEnv，提供高效的GPU并行仿真。
    """
    cfg: WarpAUVEnvCfg

    def __init__(self, cfg: WarpAUVEnvCfg, render_mode: str | None = None, **kwargs):
        """初始化WarpAUV环境

        Args:
            cfg: 环境配置对象
            render_mode: 渲染模式，可选
            **kwargs: 其他关键字参数
        """
        super().__init__(cfg, render_mode, **kwargs)

        # 调试模式标志
        self._debug = False

        # 初始化缓冲区张量
        self._actions = torch.zeros(self.num_envs, 6, device=self.device)                           # 动作缓冲区（6个推进器）
        self._thrust = torch.zeros(self.num_envs, 1, 3, device=self.device)                        # 推力缓冲区
        self._moment = torch.zeros(self.num_envs, 1, 3, device=self.device)                        # 力矩缓冲区
        self._goal = torch.zeros(self.num_envs, self.cfg.goal_dims, device=self.device)            # 目标缓冲区
        self._default_root_state = torch.zeros(self.num_envs, 13, device=self.device)              # 默认根状态（位置+姿态+速度）
        self._completion_buffer = torch.zeros(self.num_envs, device=self.device)                   # 完成状态缓冲区
        self._completed_envs = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)    # 已完成环境标记
        self._default_env_origins = torch.zeros(self.num_envs, 3, device=self.device)              # 默认环境原点
        self._goal_pos_w = self._default_env_origins                                                # 目标位置（当前仅用于可视化）
        self._step_count = 0                                                                        # 步数计数器

        # 获取推进器配置
        self.thruster_com_offsets, self.thruster_quats = get_thruster_com_and_orientations(self.device)
        # 扩展推进器配置到所有环境
        self.thruster_com_offsets = self.thruster_com_offsets.unsqueeze(0).repeat(self.num_envs, 1, 1)
        self.thruster_quats = self.thruster_quats.repeat(self.num_envs, 1)

        # 设置随机种子以确保可重现性
        torch.manual_seed(0)

        if self.cfg.eval_mode:
            print("Setting manual seed")
            torch.manual_seed(0)

        # 设置调试可视化
        self.set_debug_vis(self.cfg.debug_vis)

        if self._debug: print("mass: ", list(self._robot.root_physx_view._masses))

        # 获取AUV的特定信息
        self._gravity_magnitude = torch.tensor(self.sim.cfg.gravity, device=self.device).norm()

        # TODO: 从模型或PhysX视图获取惯性张量
        self.inertia_tensors = torch.zeros((self.num_envs, 3), device=self.device, dtype=torch.float, requires_grad=False)

        # 基于实体矩形棱柱模型的估计惯性值（估计边长：0.7m, 0.4m, 0.2m）
        # WarpAUV的虚拟惯性值，基于公式 I_ii = (1/12) * mass * (len_j² + len_k²)
        self.inertia_tensors[:, 0] = 0.37  # 绕X轴的惯性矩
        self.inertia_tensors[:, 1] = 0.97  # 绕Y轴的惯性矩
        self.inertia_tensors[:, 2] = 1.19  # 绕Z轴的惯性矩

        # 设置质量参数
        if self.cfg.mass:
            self.masses = torch.full((self.num_envs, 1), self.cfg.mass, device=self.device)
        else:
            self.masses = self._robot.root_physx_view._masses

        # TODO: 更清洁的方式来处理这个
        # 处理质心到浮心的偏移量
        if type(self.cfg.com_to_cob_offset) != torch.Tensor:
            self.com_to_cob_offsets = torch.tensor(self.cfg.com_to_cob_offset).repeat(self.num_envs, 1).to(self.device)
        else:
            self.com_to_cob_offsets = self.cfg.com_to_cob_offset.copy()

        # 处理体积参数
        if type(self.cfg.volume) != torch.Tensor:
            self.volumes = torch.full((self.num_envs, 1), self.cfg.volume, device=self.device)
        else:
            self.volumes = self.cfg.volume.copy()

        # 计算惯性张量的平均值
        self.inertia_tensors_mean = self.inertia_tensors.mean(dim=1, keepdim=True)

        # 初始化动力学计算器
        self._init_thruster_dynamics()

        # 设置初始目标
        self._reset_idx(self._robot._ALL_INDICES)


    def _init_thruster_dynamics(self):
        """初始化推进器动力学系统

        该方法初始化推进器相关的所有计算模块：
        - 流体力学力计算模型
        - 推进器动力学模型
        - 推进器转换函数
        """
        # 确保质心到浮心偏移量为张量格式
        if type(self.cfg.com_to_cob_offset) != torch.Tensor:
            self.cfg.com_to_cob_offset = torch.tensor(self.cfg.com_to_cob_offset, device=self.device, dtype=torch.float32, requires_grad=False).reshape(1,3).repeat(self.num_envs, 1)

        # 获取力计算函数和转子动力学模型
        self.force_calculation_functions = HydrodynamicForceModels(self.num_envs, self.device, False)  # 流体力学模型
        self.thruster_dynamics = DynamicsFirstOrder(self.num_envs, 6, self.cfg.dyn_time_constant, self.device)  # 一阶动力学
        self.thruster_conversion = ConversionFunctionBasic(self.cfg.rotor_constant)  # 推力转换函数

    def _setup_scene(self):
        """设置仿真场景

        该方法初始化仿真场景的所有组件：
        - 设置机器人初始状态和位置
        - 创建地面平面
        - 克隆多环境
        - 配置光照
        """
        # 设置机器人初始状态：在指定深度处开始
        self.cfg.robot_cfg.init_state = RigidObjectCfg.InitialStateCfg(pos=(0.0, 0.0, self.cfg.starting_depth))
        self._robot = RigidObject(self.cfg.robot_cfg)

        # 生成地面平面
        spawn_ground_plane(prim_path="/World/ground", cfg=GroundPlaneCfg())

        # 克隆环境（不从源复制）
        self.scene.clone_environments(copy_from_source=False)
        # 过滤碰撞（无全局路径）
        self.scene.filter_collisions(global_prim_paths=[])

        # 将机器人添加到场景中
        self.scene.articulations["robot"] = self._robot

        # 配置圆顶光照
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

    def _pre_physics_step(self, actions: torch.Tensor) -> None:
        """物理步骤前的预处理

        该方法在每个物理仿真步骤之前被调用，用于处理输入动作。

        Args:
            actions: 输入动作张量，形状为(num_envs, 6)
        """
        if self._debug: print("original actions vec: ", actions)
        if self._debug: print("concatenated actions shape: ", self._actions)

        # 复制动作并限制在[-1, 1]范围内
        self._actions[:] = actions
        self._actions[:] = torch.clip(self._actions, -1, 1).to(self.device)

    def _apply_action(self) -> None:
        """应用动作到仿真环境

        该方法计算推进器产生的推力和力矩，并将其应用到机器人上。
        """
        # 计算动力学：从动作计算推力和力矩
        self._thrust[:,0,:], self._moment[:,0,:] = self._compute_dynamics(self._actions)
        # 将外力和外力矩应用到机器人
        self._robot.set_external_force_and_torque(self._thrust, self._moment)

    def _get_observations(self) -> dict:
        """获取环境观测

        该方法构建强化学习智能体的观测向量，包括：
        - 目标姿态（四元数）
        - 相对于原点的位置偏移（机体坐标系）
        - 当前姿态（四元数）
        - 线速度（机体坐标系）
        - 角速度（机体坐标系）

        Returns:
            包含观测数据的字典，键为"policy"
        """
        # 计算相对于环境原点的位置偏移（转换到机体坐标系）
        #desired_pos_b = quat_apply(quat_conjugate(self._robot.data.root_quat_w), self._goal - self._robot.data.root_pos_w)
        offset_from_origin_b = quat_apply(quat_conjugate(self._robot.data.root_quat_w), self._default_env_origins - self._robot.data.root_pos_w)

        # 标准化和唯一化所有四元数（注释掉的代码）
        # goal = self._goal
        # root_quat_w = self._robot.data.root_quat_w
        # goal = math_utils.normalize(math_utils.quat_unique(self._goal))
        # root_quat_w = math_utils.normalize(math_utils.quat_unique(self._robot.data.root_quat_w))

        # 构建观测向量：目标 + 位置偏移 + 当前姿态 + 线速度 + 角速度
        obs = torch.cat(
            [
                self._goal,                          # 目标姿态 (4维)
                offset_from_origin_b,               # 位置偏移 (3维)
                self._robot.data.root_quat_w,       # 当前姿态 (4维)
                self._robot.data.root_lin_vel_b,    # 线速度 (3维)
                self._robot.data.root_ang_vel_b,    # 角速度 (3维)
            ],
            dim=-1  # 总计17维观测
        )
        observations = {"policy": obs}
        return observations

    def _get_rewards(self) -> torch.Tensor:
        offsets_from_origin = quat_apply(quat_conjugate(self._robot.data.root_quat_w), self._default_env_origins - self._robot.data.root_pos_w)

        total_reward = _compute_rewards(
            self.cfg.rew_scale_pos,
            self.cfg.rew_scale_ang,
            self.cfg.rew_scale_lin_vel,
            self.cfg.rew_scale_ang_vel,
            self.cfg.rew_scale_actions,
            self._robot.data.root_lin_vel_b,
            self._robot.data.root_ang_vel_b,
            self.reset_terminated,
            self._robot.data.root_pos_w,
            self._robot.data.root_quat_w,
            self._goal,
            offsets_from_origin,
            self._completed_envs,
            self._actions
        )

        return total_reward

    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        if self.cfg.cap_episode_length:
            time_out = self.episode_length_buf >= self.max_episode_length - 1
        else:
            time_out = torch.zeros(self.num_envs)

        self._step_count = self._step_count + 1

        if self.cfg.episode_length_before_reset:
            if self._step_count == self.cfg.episode_length_before_reset:
                time_out = torch.ones(self.num_envs)

        if self.cfg.use_boundaries:
            out_of_bounds = (
                (torch.abs(self._robot.data.root_pos_w[:, 0] - self.scene.env_origins[:, 0]) > self.cfg.max_auv_x) | 
                (torch.abs(self._robot.data.root_pos_w[:, 1] - self.scene.env_origins[:, 1]) > self.cfg.max_auv_y) | 
                (torch.abs(self._robot.data.root_pos_w[:, 2] - self.cfg.starting_depth) > self.cfg.max_auv_z)
            )
        else:
            out_of_bounds = torch.zeros(self.num_envs)

        return out_of_bounds, time_out

    def _reset_idx(self, env_ids: Sequence[int] | None):
        if env_ids is None:
            env_ids = self._robot._ALL_INDICES
        super()._reset_idx(env_ids)

        self._default_root_state[env_ids, :] = self._robot.data.default_root_state[env_ids]
        self._default_root_state[env_ids, :3] += self.scene.env_origins[env_ids]

        self._default_env_origins[env_ids, :] = self._default_root_state[env_ids, :3]

        if not self.cfg.eval_mode:
            # Randomize initial position relative to the origin
            self._default_root_state[env_ids, :3] += self._sample_from_sphere(len(env_ids), self.cfg.goal_spawn_radius)

            # Randomize initial orientation relative to the origin
            # self._default_root_state[env_ids, 3:7] = math_utils.random_orientation(len(env_ids), device=self.device)
            
            # Randomize initial linear and rotational velocities
            # self._default_root_state[env_ids, 7:13] = math_utils.sample_uniform(-self.cfg.init_vel_max, self.cfg.init_vel_max, (len(env_ids), 6), device=self.device)

        self._step_count = 0
        
        # Apply domain randomization
        self._reset_domain(env_ids)

        # Reset goals
        self._reset_goal(env_ids)

        if not self.cfg.eval_mode:
            # Apply guidance (set to goal position and orientation)
            envs_to_guide = math_utils.sample_uniform(0, 1, len(env_ids), self.device) < self.cfg.init_guidance_rate
            env_ids_to_guide = env_ids[envs_to_guide]
            self._default_root_state[env_ids_to_guide, :3] = self._default_env_origins[env_ids_to_guide, :3]
            self._default_root_state[env_ids_to_guide, 3:7] = self._goal[env_ids_to_guide, 0:4]

        self._robot.write_root_pose_to_sim(self._default_root_state[env_ids, :7], env_ids)
        self._robot.write_root_velocity_to_sim(self._default_root_state[env_ids, 7:], env_ids)


    # OVERRIDE THIS FUNC TO CHANGE GOAL
    def _reset_goal(self, env_ids: Sequence[int]):
        # Get random orientation
        self._goal[env_ids, 0:4] = math_utils.random_orientation(len(env_ids), device=self.device)

        # Get random yaw orientation with 0 pitch and roll
        # self._goal[env_ids,0:4] = math_utils.random_yaw_orientation(len(env_ids), device=self.device)

        # Get fix RPY
        # rs = torch.zeros(len(env_ids), device=self.device) + 0.0
        # ps = torch.zeros(len(env_ids), device=self.device) + 0.0
        # ys = torch.zeros(len(env_ids), device=self.device) + 0.0
        # self._goal[env_ids,0:4] = math_utils.quat_from_euler_xyz(rs, ps, ys)

    def _reset_domain(self, env_ids: Sequence[int]):
        self.masses[env_ids] = self.masses[env_ids]

        # Randomize COM to COB offset
        if self.cfg.domain_randomization.use_custom_randomization:
            self.com_to_cob_offsets[env_ids] = self.cfg.com_to_cob_offset[env_ids] + self._sample_from_sphere(len(env_ids), self.cfg.domain_randomization.com_to_cob_offset_radius)

        # Randomize volume
        if self.cfg.domain_randomization.use_custom_randomization:
            vol_lower, vol_upper = self.cfg.domain_randomization.volume_range
            self.volumes[env_ids] = math_utils.sample_uniform(vol_lower, vol_upper, self.volumes[env_ids].shape, self.device)

    def _sample_from_circle(self, num_env_ids, r):
        sampled_radius = r * torch.sqrt(torch.rand((num_env_ids), device=self.device))
        sampled_theta = torch.rand((num_env_ids), device=self.device) * 2 * 3.14159
        sampled_x = sampled_radius * torch.cos(sampled_theta)
        sampled_y = sampled_radius * torch.sin(sampled_theta)
        return (sampled_x, sampled_y)

    def _sample_from_sphere(self, num_env_ids, r):
        coords = torch.randn((num_env_ids, 3), device=self.device)
        norms = torch.norm(coords, dim=1).unsqueeze(1)
        coords /= norms

        radii = r * torch.pow(torch.rand((num_env_ids, 1), device=self.device), 1/3)

        return radii * coords

    def _compute_dynamics(self, actions) -> Tuple[torch.Tensor, torch.Tensor]:
        """从动作计算动力学

        该方法是整个仿真的核心，将智能体的动作转换为作用在机器人上的力和力矩。
        处理流程包括：
        1. PWM信号转换为电机转速
        2. 电机转速转换为推力
        3. 计算推进器产生的力和力矩
        4. 计算流体力学效应（浮力、阻力、粘性力）
        5. 合成总的力和力矩

        动作值范围：-1表示全反向推力，1表示全正向推力 - 这些代表PWM值
        基于：https://gitlab.com/warplab/ros/warpauv/warpauv_simulation/-/blob/master/src/robot_sim_interface.py 第91行

        Args:
            actions: 动作张量，形状为(num_envs, num_actions)

        Returns:
            Tuple[torch.Tensor, torch.Tensor]:
                - 发送到仿真的力，形状为(num_envs, 3)
                - 发送到仿真的力矩，形状为(num_envs, 3)
        """

        if self._debug: print("actions: ", actions)

        # 初始化推进器力和力矩张量
        thruster_forces = torch.zeros((self.num_envs, 6, 3), device=self.device, dtype=torch.float)
        thruster_torques = torch.zeros((self.num_envs, 6, 3), device=self.device, dtype=torch.float)
        motorValues = torch.clone(actions)  # 此时这些是-1到1之间的PWM命令

        if self._debug: print("motorValues: ", motorValues)

        # 使用WarpAUV仿真接口的方法将PWM命令转换为rad/s
        # 参考：https://gitlab.com/warplab/ros/warpauv/warpauv_simulation/-/blob/master/src/robot_sim_interface.py
        motorValues[torch.abs(motorValues) < 0.08] = 0  # 死区：小于0.08的命令设为0
        # 正向推力转换公式
        motorValues[motorValues >= 0.08] = -139.0 * (torch.pow(motorValues[motorValues >= 0.08], 2.0)) + 500 * motorValues[motorValues >= 0.08] + 8.28
        # 反向推力转换公式
        motorValues[motorValues <= -0.08] = 161.0 * (torch.pow(motorValues[motorValues <= -0.08], 2.0)) + 517.86 * motorValues[motorValues <= -0.08] - 5.72

        # 使用推进器动力学获取当前电机速度
        # TODO: 检查这里的仿真时间步长是否正确
        motorValues = self.thruster_dynamics.update(motorValues, self.episode_length_buf * self.sim.cfg.dt)

        # 使用推进器转换函数从转速获取推力
        motorValues = self.thruster_conversion.convert(motorValues)

        # TODO: 这部分可以从物理步骤中提取出来
        thruster_forces[..., 0] = 1.0  # 从X方向的力开始
        thruster_forces = quat_apply(self.thruster_quats, thruster_forces)  # 将力旋转到推进器坐标系

        # 将力的大小应用到推进器力向量
        thruster_forces = thruster_forces * motorValues.unsqueeze(-1)  # 使motorValues形状为(num_envs, 6, 1)

        # 计算推进器产生的力矩
        # T = r × F
        # T: (num_envs, num_thrusters_per_env, 3)
        # r: (num_thrusters_per_env, 3)
        # F: (num_envs, num_thrusters_per_env, 3)
        # r会广播为(num_envs, num_thrusters_per_env, 3)
        thruster_torques = torch.cross(self.thruster_com_offsets, thruster_forces, dim=-1)

        # 将所有推进器的力/力矩求和到每个机器人
        thruster_forces = torch.sum(thruster_forces, dim=-2)   # 对推进器索引求和
        thruster_torques = torch.sum(thruster_torques, dim=-2) # 对推进器索引求和

        ## 计算流体力学效应
        if self._debug: print("gravity magnitude: ", self._gravity_magnitude)

        # 计算浮力和浮力力矩
        buoyancy_forces, buoyancy_torques = self.force_calculation_functions.calculate_buoyancy_forces(
            self._robot.data.root_quat_w, self.cfg.water_rho, self.volumes, abs(self._gravity_magnitude), self.com_to_cob_offsets
        )

        # 计算密度相关阻力和粘性力
        density_forces, density_torques, viscosity_forces, viscosity_torques = self.force_calculation_functions.calculate_density_and_viscosity_forces(
            self._robot.data.root_quat_w, self._robot.data.root_lin_vel_w, self._robot.data.root_ang_vel_w,
            self.inertia_tensors, self.inertia_tensors_mean, self.cfg.water_beta, self.cfg.water_rho, self.masses
        )

        # 调试输出
        if self._debug: print("density forces: ", density_forces)
        if self._debug: print("density torques: ", density_torques)
        if self._debug: print("viscosity forces: ", viscosity_forces)
        if self._debug: print("viscosity torques: ", viscosity_torques)
        if self._debug: print("buoyancy forces: ", buoyancy_forces)
        if self._debug: print("buoyancy torques: ", buoyancy_torques)
        if self._debug: print("thruster forces: ", thruster_forces)
        if self._debug: print("thruster torques: ", thruster_torques)

        # 合成所有力和力矩
        forces = density_forces + buoyancy_forces + viscosity_forces + thruster_forces
        torques = density_torques + buoyancy_torques + viscosity_torques + thruster_torques

        if self._debug: print("final forces", forces)
        if self._debug: print("final torques", torques)

        return forces, torques

    def _set_debug_vis_impl(self, debug_vis: bool):
        # create markers if necessary for the first tome
        if debug_vis:
            if not hasattr(self, "goal_pos_visualizer"):
                marker_cfg = CUBOID_MARKER_CFG.copy()
                marker_cfg.markers["cuboid"].size = (0.05, 0.05, 0.05)
                # -- goal pose
                marker_cfg.prim_path = "/Visuals/Command/goal_position"
                self.goal_pos_visualizer = VisualizationMarkers(marker_cfg)

            if not hasattr(self, "goal_ang_visualizer"):
                marker_cfg = RED_ARROW_X_MARKER_CFG.copy()
                marker_cfg.prim_path = "/Visuals/Command/goal_ang"
                marker_cfg.markers["arrow"].scale = (0.125, 0.125, 1)
                self.goal_ang_visualizer = VisualizationMarkers(marker_cfg)

            if not hasattr(self, "goal_z_ang_visualizer"):
                marker_cfg = BLUE_ARROW_X_MARKER_CFG.copy()
                marker_cfg.prim_path = "/Visuals/Command/goal_z_ang"
                marker_cfg.markers["arrow"].scale = (0.125, 0.125, 1)
                self.goal_z_ang_visualizer = VisualizationMarkers(marker_cfg)

            if not hasattr(self, "x_b_visualizer"):
                marker_cfg = GREEN_ARROW_X_MARKER_CFG.copy()
                marker_cfg.markers["arrow"].scale = (0.125, 0.125, 1)
                marker_cfg.prim_path = "/Visuals/Command/x_b"
                self.x_b_visualizer = VisualizationMarkers(marker_cfg)

            if not hasattr(self, "z_b_visualizer"):
                marker_cfg = GREEN_ARROW_X_MARKER_CFG.copy()
                marker_cfg.markers["arrow"].scale = (0.125, 0.125, 1)
                marker_cfg.prim_path = "/Visuals/Command/z_b"
                self.z_b_visualizer = VisualizationMarkers(marker_cfg)
            
            # set their visibility to true
            self.goal_pos_visualizer.set_visibility(True)
            self.goal_ang_visualizer.set_visibility(True)
            self.goal_z_ang_visualizer.set_visibility(True)
            self.x_b_visualizer.set_visibility(True)
            self.z_b_visualizer.set_visibility(True)

        else:
            if hasattr(self, "goal_pos_visualizer"):
                self.goal_pos_visualizer.set_visibility(False)

            if hasattr(self, "goal_ang_visualizer"):
                self.goal_ang_visualizer.set_visibility(False)

            if hasattr(self, "goal_z_ang_visualizer"):
                self.goal_z_ang_visualizer.set_visibility(False)

            if hasattr(self, "x_b_visualizer"):
                self.x_b_visualizer.set_visibility(False)
            
            if hasattr(self, "z_b_visualizer"):
                self.z_b_visualizer.set_visibility(False)

    def _rotate_quat_by_euler_xyz(self, q: torch.tensor, x: float|torch.tensor, y: float|torch.tensor, z: float|torch.tensor, device=None):
        # Assumes q has shape [num_envs, 4]
        num_envs = q.shape[0]
        if device == None:
            device = self.device

        if type(x) == float:
            x = torch.zeros(num_envs, device=device) + x

        if type(y) == float:
            y = torch.zeros(num_envs, device=device) + y
        
        if type(z) == float:
            z = torch.zeros(num_envs, device=device) + z

        iq = math_utils.quat_from_euler_xyz(x, y, z)
        return math_utils.quat_mul(q, iq)


    def _debug_vis_callback(self, event):
        # Visualize the goal positions
        # self.goal_pos_visualizer.visualize(translations = self._default_env_origins)
        self.goal_pos_visualizer.visualize(translations = self._goal_pos_w)

        # Visualize goal orientations
        goal_quats_w = self._goal
        ang_marker_scales = torch.tensor([1, 1, 1]).repeat(self.num_envs, 1)
        ang_marker_scales[:, 0] = 1
        self.goal_ang_visualizer.visualize(translations=self._robot.data.root_pos_w, orientations=goal_quats_w, scales=ang_marker_scales)

        # Visualize goal orientations via another axis
        goal_z_quat = self._rotate_quat_by_euler_xyz(goal_quats_w, 0.0, -torch.pi/2, 0.0)
        ang_marker_scales = torch.tensor([1, 1, 1]).repeat(self.num_envs, 1)
        ang_marker_scales[:, 0] = 1
        self.goal_z_ang_visualizer.visualize(translations=self._robot.data.root_pos_w, orientations=goal_z_quat, scales=ang_marker_scales)

        # Visualize current X-direction
        x_w = self._robot.data.root_quat_w
        x_w_marker_scales = torch.tensor([1, 1, 1]).repeat(self.num_envs, 1)
        x_w_marker_scales[:, 0] = 1
        self.x_b_visualizer.visualize(translations=self._robot.data.root_pos_w, orientations=x_w, scales=x_w_marker_scales)

        # Visualize current Z-direction
        z_w_quat = self._rotate_quat_by_euler_xyz(self._robot.data.root_quat_w, 0.0, -torch.pi/2, 0.0)
        z_w_marker_scales = torch.tensor([1, 1, 1]).repeat(self.num_envs, 1)
        z_w_marker_scales[:, 0] = 1
        self.z_b_visualizer.visualize(translations=self._robot.data.root_pos_w, orientations=z_w_quat, scales=z_w_marker_scales)


@torch.jit.script
def quat_dist(q1, q2):
    """计算四元数距离

    计算两个四元数之间的距离度量。

    Args:
        q1: 第一个四元数
        q2: 第二个四元数

    Returns:
        四元数距离值
    """
    return 1 - torch.sum(q1*q2, dim=-1)**2

@torch.jit.script
def _compute_rewards(
    rew_scale_pos: float,
    rew_scale_ang: float,
    rew_scale_lin_vel: float,
    rew_scale_ang_vel: float,
    rew_scale_actions: float,
    lin_vel: torch.Tensor,
    ang_vel: torch.Tensor,
    reset_terminated: torch.Tensor,
    root_pos: torch.Tensor,
    root_quat: torch.Tensor,
    goal: torch.Tensor,
    offsets_from_origin: torch.Tensor,
    completed_envs: torch.Tensor,
    actions: torch.Tensor,
):
    """计算强化学习奖励

    该函数计算多项奖励分量的加权和，用于指导智能体学习：
    - 位置精度奖励：鼓励接近目标位置
    - 角度精度奖励：鼓励达到目标姿态
    - 角速度惩罚：鼓励平稳运动
    - 动作惩罚：鼓励节能控制

    使用指数函数形式的奖励，在目标附近提供更强的梯度信号。

    Args:
        rew_scale_pos: 位置奖励权重
        rew_scale_ang: 角度奖励权重
        rew_scale_lin_vel: 线速度奖励权重（未使用）
        rew_scale_ang_vel: 角速度奖励权重
        rew_scale_actions: 动作奖励权重
        lin_vel: 线速度张量
        ang_vel: 角速度张量
        reset_terminated: 重置终止标志
        root_pos: 根位置
        root_quat: 根姿态四元数
        goal: 目标四元数
        offsets_from_origin: 相对于原点的偏移
        completed_envs: 已完成环境标志
        actions: 动作张量

    Returns:
        总奖励张量
    """

    # 位置精度奖励，TODO: 适当缩放高斯标准差
    rew_pos = rew_scale_pos * torch.exp(-1 * torch.norm(offsets_from_origin, dim=1)**2)

    # 角度精度奖励，TODO: 适当缩放高斯标准差
    # 标准化和唯一化所有四元数
    rew_ang = rew_scale_ang * torch.exp(-1 * math_utils.quat_error_magnitude(goal[:,:], root_quat[:,:]))

    # 角速度惩罚：鼓励平稳运动
    rew_ang_vel = rew_scale_ang_vel * torch.exp(-1 * torch.norm(ang_vel, dim=1)**2)

    # 能耗惩罚：鼓励节能控制
    rew_action = rew_scale_actions * torch.exp(-1 * torch.norm(actions, dim=1)**2)

    # 总奖励为各分量的加权和
    total_rew = rew_ang + rew_action + rew_pos + rew_ang_vel
    return total_rew
