"""
刚体流体力学计算模块

该模块用于计算刚体在流体中受到的流体力学力和力矩，包括浮力、阻力和粘性力。
基于MuJoCo流体力学模型的描述实现: https://mujoco.readthedocs.io/en/3.0.1/computation/fluid.html

主要功能:
- 计算浮力和浮力力矩
- 计算二次阻力(与速度平方成正比)
- 计算线性粘性力(与速度成正比)
- 支持多环境并行计算

作者: <PERSON> Fahnestock and Levi "Veevee" Cai (<EMAIL>)
"""

from dataclasses import dataclass
from typing import Tuple
from isaaclab.utils.math import quat_conjugate, quat_inv, quat_apply, convert_quat
import numpy as np
import torch

@dataclass
class HydrodynamicForceModels:
    """流体力学力模型类

    该类实现了水下刚体的各种流体力学效应计算，包括浮力、阻力和粘性力。
    所有计算都支持多环境并行处理，适用于强化学习训练场景。

    属性:
        num_envs: 环境数量
        device: 计算设备(CPU或GPU)
        debug: 是否启用调试模式，输出详细计算信息
    """
    num_envs: int
    device: torch.device
    debug: bool = False

    def calculate_buoyancy_forces(self,
                                  root_quats_w: torch.tensor, # 机器人在世界坐标系中的姿态四元数
                                  fluid_density: float, # 流体密度
                                  volumes: torch.tensor, # 刚体体积
                                  g_mag: float, # 重力加速度大小
                                  com_to_cob_offsets:torch.tensor) -> Tuple[torch.tensor, torch.tensor]:
        """计算完全浸没刚体的浮力和浮力力矩

        该方法计算刚体在流体中受到的浮力作用，包括浮力和由浮力产生的力矩。
        返回的力和力矩都在机体坐标系中表示。
        注意：重力由Isaac Sim默认施加，此处只计算浮力。

        Args:
            root_quats_w: 机器人在世界坐标系中的姿态四元数，形状为(num_envs, 4)
            fluid_density: 流体密度 (kg/m³)
            volumes: 刚体体积，形状为(num_envs, 1)
            g_mag: 重力加速度大小 (m/s²)
            com_to_cob_offsets: 质心到浮心的偏移向量，形状为(num_envs, 3)

        Returns:
            Tuple[torch.tensor, torch.tensor]:
                - 浮力在机体坐标系中的表示，形状为(num_envs, 3)
                - 浮力力矩在机体坐标系中的表示，形状为(num_envs, 3)
        """

        # 初始化浮力和浮力力矩张量
        buoyancy_forces_b = torch.zeros((self.num_envs, 3), device=self.device, dtype=torch.float)
        buoyancy_torques_b = torch.zeros((self.num_envs, 3), device=self.device, dtype=torch.float)

        # 在世界坐标系中定义浮力方向(向上，与重力相反)
        buoyancy_directions_w = torch.zeros((self.num_envs, 3), device=self.device, dtype=torch.float)
        buoyancy_directions_w[..., 2] = 1.0 # Z轴正方向，与重力方向相反

        if self.debug: print(f"shape of root_quats: {root_quats_w.shape}, shape of buoyancy_vectors: {buoyancy_directions_w.shape}")

        # 将浮力方向从世界坐标系转换到机体坐标系
        buoyancy_directions_b = quat_apply(quat_conjugate(root_quats_w), buoyancy_directions_w)

        # 计算浮心处的浮力大小：F_buoyancy = ρ * V * g
        # TODO: 理论上应该在机器人根部计算浮力，而不是浮心，但这两者是否相同？
        buoyancy_forces_at_cob_b = buoyancy_directions_b * fluid_density * volumes.repeat(1,3) * g_mag
        buoyancy_forces_b = buoyancy_forces_at_cob_b

        # 计算浮力产生的力矩：τ = r × F，其中r是质心到浮心的向量
        buoyancy_torques_b = torch.cross(com_to_cob_offsets, buoyancy_forces_at_cob_b, dim=-1)

        if self.debug: print(f"Calculated buoyancy values: forces are {buoyancy_forces_b} and torques are {buoyancy_torques_b}")

        return (buoyancy_forces_b, buoyancy_torques_b)
  
    def _calculate_inferred_half_dimensions(self, inertias, masses):
        """计算等效惯性盒的推断半尺寸

        该方法根据惯性张量和质量计算等效矩形盒子的半尺寸。
        这个等效盒子用于近似计算流体阻力，是一种简化的几何建模方法。

        Args:
            inertias: 惯性张量的对角元素，形状为(num_envs, 3)
            masses: 质量，形状为(num_envs, 1)

        Returns:
            等效盒子的半尺寸，形状为(num_envs, 3)
        """
        # 基于惯性张量反推等效盒子尺寸的公式
        # 对于矩形盒子：I_ii = (1/12) * m * (l_j² + l_k²)
        # 反推得到：r_i = sqrt(3/(2*m) * (I_j + I_k - I_i))
        r = torch.sqrt( (3/(2 * masses.repeat(1,3))) * (torch.roll(inertias, 1, 1) + torch.roll(inertias, -1, 1) - inertias))
        return r

    def calculate_quadratic_drag_forces(self,
                                      root_linvels_b: torch.tensor,
                                      root_angvels_b: torch.tensor,
                                      inertias: torch.tensor,
                                      masses: torch.tensor,
                                      fluid_density_rho
                                      ):
        """计算二次阻力和阻力力矩

        该方法计算与速度平方成正比的阻力，这是高雷诺数流动的主要阻力形式。
        阻力方向与速度方向相反，大小与速度平方成正比。

        Args:
            root_linvels_b: 机体坐标系中的线速度，形状为(num_envs, 3)
            root_angvels_b: 机体坐标系中的角速度，形状为(num_envs, 3)
            inertias: 惯性张量对角元素，形状为(num_envs, 3)
            masses: 质量，形状为(num_envs, 1)
            fluid_density_rho: 流体密度

        Returns:
            Tuple[torch.tensor, torch.tensor]:
                - 二次阻力，形状为(num_envs, 3)
                - 二次阻力力矩，形状为(num_envs, 3)
        """

        # 计算等效盒子的三个半尺寸
        ri = self._calculate_inferred_half_dimensions(inertias, masses)
        rj = torch.roll(ri, 1, 1)  # 循环移位获得相邻维度
        rk = torch.roll(ri, -1, 1) # 循环移位获得另一相邻维度

        # 线性阻力：F_drag = -2 * ρ * A * |v| * v，其中A = rj * rk为截面积
        forces = -2. * fluid_density_rho * rj * rk * torch.abs(root_linvels_b) * root_linvels_b

        # 角阻力力矩：τ_drag = -0.5 * ρ * I_area * |ω| * ω，其中I_area为面积惯性矩
        torques = -0.5 * fluid_density_rho * ri * (torch.pow(rj,4) + torch.pow(rk,4)) * torch.abs(root_angvels_b) * root_angvels_b

        return (forces, torques)

    def calculate_linear_viscous_forces(self,
                                        root_linvels_b: torch.tensor,
                                        root_angvels_b: torch.tensor,
                                        inertias: torch.tensor,
                                        masses,
                                        fluid_viscosity_beta
                                        ):
        """计算线性粘性力和粘性力矩

        该方法计算与速度成正比的粘性力，这是低雷诺数流动的主要阻力形式。
        粘性力基于斯托克斯阻力公式，适用于层流条件。

        Args:
            root_linvels_b: 机体坐标系中的线速度，形状为(num_envs, 3)
            root_angvels_b: 机体坐标系中的角速度，形状为(num_envs, 3)
            inertias: 惯性张量对角元素，形状为(num_envs, 3)
            masses: 质量
            fluid_viscosity_beta: 流体动力粘度

        Returns:
            Tuple[torch.tensor, torch.tensor]:
                - 线性粘性力，形状为(num_envs, 3)
                - 线性粘性力矩，形状为(num_envs, 3)
        """
        # 计算等效半径
        ri = self._calculate_inferred_half_dimensions(inertias, masses)
        r_eq = torch.mean(ri, 1, keepdim=True)  # 取平均作为等效半径

        r_eq = r_eq.repeat(1,3)
        # 斯托克斯阻力：F_viscous = -6πβr*v (球体近似)
        forces = -6. * fluid_viscosity_beta * torch.pi * r_eq * root_linvels_b
        # 粘性力矩：τ_viscous = -8πβr³*ω (球体近似)
        torques = -8. * fluid_viscosity_beta * torch.pi * torch.pow(r_eq, 3) * root_angvels_b
        return (forces, torques)

    def calculate_density_and_viscosity_forces(self,
                                               root_quats_w: torch.tensor,
                                               root_linvels_w:torch.tensor, #[num_envs, 3]
                                               root_angvels_w:torch.tensor, #[num_envs, 3]
                                               inertias: torch.Tensor, #[num_envs, 3]
                                               inertias_mean: torch.Tensor, #[num_envs, 1]
                                               water_beta: float,
                                               water_rho: float,
                                               masses: torch.tensor
                                               ):
        """计算密度相关阻力和粘性力

        该方法是一个综合接口，计算所有与流体密度和粘性相关的力和力矩。
        包括二次阻力(密度相关)和线性粘性力。

        Args:
            root_quats_w: 世界坐标系中的姿态四元数，形状为[num_envs, 4]
            root_linvels_w: 世界坐标系中的线速度，形状为[num_envs, 3]
            root_angvels_w: 世界坐标系中的角速度，形状为[num_envs, 3]
            inertias: 惯性张量对角元素，形状为[num_envs, 3]
            inertias_mean: 惯性张量平均值，形状为[num_envs, 1]
            water_beta: 水的动力粘度
            water_rho: 水的密度
            masses: 质量张量

        Returns:
            Tuple[torch.tensor, torch.tensor, torch.tensor, torch.tensor]:
                - f_d: 二次阻力，形状为[num_envs, 3]
                - g_d: 二次阻力力矩，形状为[num_envs, 3]
                - f_v: 线性粘性力，形状为[num_envs, 3]
                - g_v: 线性粘性力矩，形状为[num_envs, 3]
        """

        # 将世界坐标系的速度转换到机体坐标系
        root_quats_b = quat_conjugate(root_quats_w)
        root_linvels_b = quat_apply(root_quats_b, root_linvels_w)
        root_angvels_b = quat_apply(root_quats_b, root_angvels_w)

        # 分别计算二次阻力和线性粘性力
        f_d, g_d = self.calculate_quadratic_drag_forces(root_linvels_b, root_angvels_b, inertias, masses, water_rho)
        f_v, g_v = self.calculate_linear_viscous_forces(root_linvels_b, root_angvels_b, inertias, masses, water_beta)
        return (f_d, g_d, f_v, g_v)

if __name__ == "__main__":
    """主函数 - 单元测试

    该部分包含了流体力学模型的单元测试，验证浮力计算的正确性。
    测试不同姿态下的浮力和浮力力矩计算结果。
    """
    # 设置计算设备，优先使用GPU
    device=torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

    # 水的物理参数
    water_rho = 997.0 # 水密度 kg/m³
    water_beta = 0.001306 # 水的动力粘度 Pa·s，50°F时的值
    g_mag = 9.81 # 重力加速度 m/s²
    num_envs = 4 # 测试环境数量

    # 质心到浮心的偏移量 (x, y, z) 单位：米
    com_to_cob_offset = torch.tensor([0.0, 0.0, 0.3], dtype=torch.float, device=device, requires_grad=False).reshape(1,3).repeat(num_envs, 1)
    volume = 0.022747843530591776 # 体积，单位：立方米 - 中性浮力设计

    # 创建流体力学模型实例，启用调试模式
    forceModel = HydrodynamicForceModels(num_envs, device, True)

    # 定义测试用的四元数姿态
    root_quats = torch.tensor([[0.0, 0.0, 0.0, 1.0], # 无旋转
                               [-0.7071068, 0, 0, 0.7071068], # 绕X轴旋转90度
                               [ 0, -0.7071068, 0, 0.7071068 ], # 绕Y轴旋转90度
                               [ 0.3535534, 0.3535534, 0.1464466, 0.8535534 ], # 复合旋转

    ]).to(device)

    # 预期的浮力结果（理论值），用于验证计算正确性
    true_b_forces = torch.tensor([[0.0, 0.0, volume * water_rho * g_mag], # 无旋转：浮力向上
                                  [0.0, -1 * volume * water_rho * g_mag, 0.0], # X轴90度旋转：浮力向-Y
                                  [ volume * water_rho * g_mag, 0.0, 0.0], # Y轴90度旋转：浮力向+X
                                  [ -0.5 * volume * water_rho * g_mag, 0.7071 * volume * water_rho * g_mag, 0.5 * volume * water_rho * g_mag], # 复合旋转

    ]).to(device)

    # 预期的浮力力矩结果（理论值）
    true_b_torques = torch.tensor([[0.0, 0.0, 0.0], # 无旋转：无力矩
                                    [0.3 * water_rho * g_mag * volume, 0.0, 0.0], # X轴旋转：绕X轴力矩
                                    [0.0, 0.3 * water_rho * g_mag * volume, 0.0], # Y轴旋转：绕Y轴力矩
                                    [-0.3 * 0.7071 * water_rho * g_mag * volume, -0.15 * water_rho * g_mag * volume, 0.0], # 复合旋转
    ]).to(device)

    # 执行浮力计算
    b_force, b_torque = forceModel.calculate_buoyancy_forces( root_quats, water_rho, volume, g_mag, com_to_cob_offset)

    # 验证浮力计算结果
    if(np.abs(b_force.cpu().numpy() - true_b_forces.cpu().numpy()).max() > 1e-9):
        print(f"ERROR: b_force is\n {b_force} \nand true_b_forces is \n {true_b_forces}\n with max value {np.abs(b_force.cpu().numpy() - true_b_forces.cpu().numpy()).max()}")
    if (np.abs(b_torque.cpu().numpy() - true_b_torques.cpu().numpy()).max() > 1e-9):
        print(f"ERROR: b_torque is\n {b_torque} and true_b_torques is\n {true_b_torques}")

    # 测试用的速度数据（当前为零速度）
    root_linvels = torch.tensor([[[0.0, 0.0, 0.0]],
                                 [[0.0, 0.0, 0.0]],
    ])
    root_angvels = torch.tensor([[[0.0, 0.0, 0.0]],
                                 [[0.0, 0.0, 0.0]],
    ])

    # TODO: 添加惯性张量测试
    #env_inertia_tensors =
    #dens_force, dense_torqe, visc_force, visc_torque = forceModel.calculate_density_and_viscosity_forces(root_linvels, root_angvels, env_inertia_tensors, water_beta, water_rho)