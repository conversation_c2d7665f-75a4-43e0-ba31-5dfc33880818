"""
WarpAUV推进器动力学和模型

该模块实现了水下机器人推进器的动力学建模，包括：
- 推进器位置和姿态配置
- 推进器动态响应模型
- PWM信号到推力的转换函数
- 一阶动力学系统建模

基于UUV仿真器的实现：
https://github.com/uuvsimulator/uuv_simulator/blob/master/uuv_gazebo_plugins/uuv_gazebo_plugins/src/Dynamics.cc

作者: Ethan Fahnestock
"""

from isaaclab.utils.math import quat_from_euler_xyz
from dataclasses import dataclass
from abc import ABC, abstractmethod
import numpy as np
import torch

def get_thruster_com_and_orientations(device):
    """获取推进器的质心偏移和姿态信息

    该函数定义了WarpAUV的6个推进器的位置和姿态配置。
    TODO: 这个函数应该通过USD/URDF模型和配置文件来处理，使用命名的执行器。

    Args:
        device: 计算设备(CPU或GPU)

    Returns:
        Tuple[torch.Tensor, torch.Tensor]:
            - thruster_com_offsets: 推进器相对于质心的位置偏移，形状为(6, 3)
            - thruster_quats: 推进器的姿态四元数，形状为(6, 4)
    """
    def create_tf_rpy(x,y,z,rr,rp,ry):
        """通过RPY角度创建变换

        Args:
            x,y,z: 位置坐标
            rr,rp,ry: 滚转、俯仰、偏航角度(弧度)

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 位置向量和姿态四元数
        """
        print(rr,rp,ry)
        shift = torch.Tensor([x, y, z])
        r = quat_from_euler_xyz(torch.Tensor([rr]), torch.Tensor([rp]), torch.Tensor([ry]))[0]
        print(rr, rp, ry, r[0], r[1], r[2], r[3])
        return shift, r

    def create_tf_quat(x,y,z,w,vx,vy,vz):
        """通过四元数创建变换

        Args:
            x,y,z: 位置坐标
            w,vx,vy,vz: 四元数分量

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 位置向量和姿态四元数
        """
        shift = torch.Tensor([x, y, z])
        r = torch.Tensor([w, vx, vy, vz])
        return shift, r

    # TODO: 考虑重构这个格式，去掉辅助函数
    # 定义推进器配置信息字典
    thruster_info = dict(
        drive_left=create_tf_quat(-0.4127, .1506, -0.0889, 1,0,0,0),      # 左驱动推进器
        drive_right = create_tf_quat(-0.4127,-.1506,-0.0889,1,0,0,0),     # 右驱动推进器
        rear_left = create_tf_rpy(-0.303, 0.1461, -0.1587, 0, -0.785398, 1.5708),    # 左后推进器
        rear_right = create_tf_rpy(-0.303, -0.1461, -0.1587, 0, -0.785398, -1.5708), # 右后推进器
        front_right = create_tf_rpy(0.0585, -0.1461, -0.0540, 0, 0.785398,-1.5708),  # 右前推进器
        front_left = create_tf_rpy(0.0585, 0.1461, -0.0540, 0, 0.785398, 1.5708),    # 左前推进器
    )

    # 从质心指向推进器位置的向量 (推进器数量, 3)
    # 推进器编号顺序：
    # 0 - drive_left   (左驱动)
    # 1 - drive_right  (右驱动)
    # 2 - rear_left    (左后)
    # 3 - rear_right   (右后)
    # 4 - front_left   (左前)
    # 5 - front_right  (右前)
    thruster_com_offsets = torch.tensor([
        [thruster_info["drive_left"][0][0], thruster_info["drive_left"][0][1], thruster_info["drive_left"][0][2]],
        [thruster_info["drive_right"][0][0], thruster_info["drive_right"][0][1], thruster_info["drive_right"][0][2]],
        [thruster_info["rear_left"][0][0], thruster_info["rear_left"][0][1], thruster_info["rear_left"][0][2]],
        [thruster_info["rear_right"][0][0], thruster_info["rear_right"][0][1], thruster_info["rear_right"][0][2]],
        [thruster_info["front_left"][0][0], thruster_info["front_left"][0][1], thruster_info["front_left"][0][2]],
        [thruster_info["front_right"][0][0], thruster_info["front_right"][0][1], thruster_info["front_right"][0][2]]
    ], dtype=torch.float32, device=device, requires_grad=False)

    # 从质心坐标系到推进器坐标系的姿态四元数 (推进器数量, 4)
    thruster_quats = torch.tensor([
        [thruster_info["drive_left"][1][0], thruster_info["drive_left"][1][1], thruster_info["drive_left"][1][2], thruster_info["drive_left"][1][3]],
        [thruster_info["drive_right"][1][0], thruster_info["drive_right"][1][1], thruster_info["drive_right"][1][2], thruster_info["drive_right"][1][3]],
        [thruster_info["rear_left"][1][0], thruster_info["rear_left"][1][1], thruster_info["rear_left"][1][2], thruster_info["rear_left"][1][3]],
        [thruster_info["rear_right"][1][0], thruster_info["rear_right"][1][1], thruster_info["rear_right"][1][2], thruster_info["rear_right"][1][3]],
        [thruster_info["front_left"][1][0], thruster_info["front_left"][1][1], thruster_info["front_left"][1][2], thruster_info["front_left"][1][3]],
        [thruster_info["front_right"][1][0], thruster_info["front_right"][1][1], thruster_info["front_right"][1][2], thruster_info["front_right"][1][3]]
    ], dtype=torch.float32, device=device, requires_grad=False)

    return thruster_com_offsets, thruster_quats


class Dynamics(ABC):
    """推进器动力学抽象基类

    该抽象类定义了推进器动力学模型的基本接口。
    所有具体的动力学模型都应该继承这个类并实现update方法。

    属性:
        numEnvs: 环境数量
        num_thrusters_per_env: 每个环境的推进器数量
        device: 计算设备
        state: 推进器状态张量
        prevTime: 上一次更新的时间
    """

    def __init__(self, numEnvs:int, num_thrusters_per_env:int, device:torch.device) -> None:
        """初始化动力学模型

        Args:
            numEnvs: 环境数量
            num_thrusters_per_env: 每个环境的推进器数量
            device: 计算设备
        """
        self.numEnvs = numEnvs
        self.num_thrusters_per_env = num_thrusters_per_env
        self.device = device
        self.reset_all()

    def reset(self, maskArr:list):
        """重置指定环境的推进器状态

        Args:
            maskArr: 布尔数组，大小为(numEnvs)，值为True的环境将被重置
        """
        self.state[maskArr,:] = 0.0
        self.prevTime[maskArr] = -1.0

    def reset_all(self):
        """重置所有环境的推进器状态

        初始化状态张量和时间记录。
        """
        self.state = torch.zeros((self.numEnvs, self.num_thrusters_per_env), dtype=torch.float32, device=self.device, requires_grad=False)
        self.prevTime = torch.ones((self.numEnvs), dtype=torch.float32, device=self.device, requires_grad=False) * -1.0

    @abstractmethod
    def update(self, cmd:torch.tensor, t:float) -> float:
        """更新推进器状态（抽象方法）

        Args:
            cmd: 推进器命令张量
            t: 当前时间

        Returns:
            更新后的推进器状态
        """
        pass

class DynamicsFirstOrder(Dynamics):
    """一阶推进器动力学模型

    该类实现了一阶线性动力学系统，模拟推进器的动态响应。
    推进器输出不会立即跟随命令变化，而是以指数形式逐渐接近目标值。

    动力学方程: τ * dF/dt + F = F_cmd
    其中 τ 是时间常数，F 是推进器输出，F_cmd 是命令输入。
    """

    def __init__(self, numEnvs:int, num_thrusters_per_env:int, tau:float, device:torch.device):
        """初始化一阶动力学模型

        Args:
            numEnvs: 环境数量
            num_thrusters_per_env: 每个环境的推进器数量
            tau: 时间常数，控制响应速度
            device: 计算设备
        """
        super().__init__(numEnvs=numEnvs, num_thrusters_per_env=num_thrusters_per_env, device=device)
        self.tau = tau

    def update(self, cmd:torch.tensor, t:torch.tensor) -> float:
        """更新推进器动力学状态

        根据一阶动力学方程更新推进器状态。使用指数衰减来模拟动态响应。

        Args:
            cmd: 推进器命令张量，形状为(numEnvs, num_thrusters_per_env)
            t: 当前时间张量，形状为(numEnvs)

        Returns:
            更新后的推进器状态张量
        """
        # 旧方法：如果时间未设置则直接返回状态
        #if self.prevTime < 0:
        #  self.prevTime = t
        #  return self.state

        # 为之前未更新的环境设置当前时间
        self.prevTime[self.prevTime < 0] = t[self.prevTime < 0]

        # 计算时间步长和衰减因子
        # 对于之前未更新的时间，dt=0，alpha=1，保持之前的状态
        dt = t - self.prevTime
        alpha = torch.exp(-dt/self.tau)  # 指数衰减因子
        alpha = torch.zeros_like(alpha)  # TODO: 这里将alpha清零，总是直接设置为命令值！
        #print(self.state.shape, cmd.shape, alpha.shape)
        #print(dt, alpha, self.state)

        # 一阶动力学更新：state = alpha * old_state + (1-alpha) * cmd
        self.state = self.state * alpha.unsqueeze(-1) + (1.0 - alpha).unsqueeze(-1) * cmd
        assert torch.any(self.state == cmd)

        # 更新时间记录
        self.prevTime = t
        return self.state

# 基于UUV仿真器的推进器转换函数实现：
# https://github.com/uuvsimulator/uuv_simulator/blob/master/uuv_gazebo_plugins/uuv_gazebo_plugins/src/ThrusterConversionFcn.cc
@dataclass
class ConversionFunction(ABC):
    """推进器转换函数抽象基类

    该抽象类定义了将推进器命令转换为推力的接口。
    不同类型的推进器可能有不同的转换特性。
    """

    @abstractmethod
    def convert(self, cmd:np.ndarray) -> float:
        """转换命令到推力（抽象方法）

        Args:
            cmd: 推进器命令数组

        Returns:
            转换后的推力值
        """
        pass

class ConversionFunctionBasic(ConversionFunction):
    """基础推进器转换函数

    实现简单的二次转换关系：推力 = 转子常数 * |命令| * 命令
    这种关系考虑了推进器在正反转时的非线性特性。

    属性:
        rotorConstant: 转子常数，决定命令到推力的转换比例
    """

    rotorConstant: float  # 转子常数

    def __init__(self, rotorConstant:float):
        """初始化基础转换函数

        Args:
            rotorConstant: 转子常数
        """
        super().__init__()
        self.rotorConstant = rotorConstant

    def convert(self, cmd:torch.tensor) -> float:
        """将速度命令转换为推力

        使用二次关系进行转换，保持命令的符号以区分正反转。

        Args:
            cmd: 推进器命令张量，形状为(numEnvs, num_thrusters_per_env)

        Returns:
            转换后的推力张量
        """
        return self.rotorConstant * torch.abs(cmd) * cmd
  